2025-09-01 13:12:08,289 - INFO - Density Prediction Pipeline Started
2025-09-01 13:12:08,289 - INFO - Setting up and validating paths...
2025-09-01 13:12:08,289 - INFO - [SUCCESS] Paths validated:
2025-09-01 13:12:08,289 - INFO -    Training: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A1.hdf5
2025-09-01 13:12:08,289 - INFO -    Validation: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A2.hdf5
2025-09-01 13:12:08,289 - INFO -    Test: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A2.hdf5
2025-09-01 13:12:08,302 - INFO -    Save: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline
2025-09-01 13:12:08,320 - INFO - Using device: cuda:0
2025-09-01 13:12:08,320 - INFO -    GPU: NVIDIA T550 Laptop GPU
2025-09-01 13:12:08,320 - INFO -    Memory: 4.0 GB
2025-09-01 13:12:08,320 - INFO - Starting Density Prediction Pipeline
2025-09-01 13:12:08,320 - INFO -    Input curves: ['GR', 'AC', 'CNL', 'RLLD']
2025-09-01 13:12:08,320 - INFO -    Output curves: ['DEN']
2025-09-01 13:12:08,320 - INFO -    Model type: base
2025-09-01 13:12:08,320 - INFO -    Training file: A1.hdf5
2025-09-01 13:12:08,320 - INFO -    Validation file: A2.hdf5
2025-09-01 13:12:08,320 - INFO -    Test file: A2.hdf5
2025-09-01 13:12:08,335 - INFO - Starting training phase...
2025-09-01 13:12:08,335 - INFO - Hyperparameters saved to C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline\hyperparameter.txt
2025-09-01 13:12:08,337 - INFO - Loading training and validation datasets...
2025-09-01 13:12:08,337 - INFO -    Training file: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A1.hdf5
2025-09-01 13:12:08,338 - INFO -    Validation file: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A2.hdf5
2025-09-01 13:12:08,338 - INFO -    Training samples: 14
2025-09-01 13:12:08,338 - INFO -    Validation samples: 14
2025-09-01 13:12:08,338 - INFO -    Batch size: 32
2025-09-01 13:12:08,338 - INFO - Creating base model...
2025-09-01 13:12:08,638 - INFO -    Total parameters: 926,849
2025-09-01 13:12:08,638 - INFO -    Trainable parameters: 926,849
2025-09-01 13:12:10,818 - INFO - Starting training for 2000 epochs...
2025-09-01 13:12:15,440 - ERROR - [ERROR] Pipeline failed: The size of tensor a (720) must match the size of tensor b (640) at non-singleton dimension 1
