2025-09-01 12:54:27,228 - WARNING - Training path not found: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\train
2025-09-01 12:54:27,228 - INFO - Using fallback training data: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
2025-09-01 12:54:27,237 - INFO -    Training: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
2025-09-01 12:54:27,237 - INFO -    Validation: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
2025-09-01 12:54:27,237 - INFO -    Test: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
2025-09-01 12:54:27,237 - INFO -    Save: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_large_model
2025-09-01 12:54:27,299 - INFO -    GPU: NVIDIA T550 Laptop GPU
2025-09-01 12:54:27,315 - INFO -    Memory: 4.0 GB
2025-09-01 12:54:27,325 - INFO -    Input curves: ['GR', 'AC', 'CNL', 'RLLD']
2025-09-01 12:54:27,327 - INFO -    Output curves: ['DEN']
2025-09-01 12:54:27,327 - INFO -    Model type: large
2025-09-01 12:54:27,340 - INFO -    Training samples: 5
2025-09-01 12:54:27,340 - INFO -    Validation samples: 5
2025-09-01 12:54:27,340 - INFO -    Batch size: 16
2025-09-01 12:54:27,613 - INFO -    Total parameters: 2,276,993
2025-09-01 12:54:27,613 - INFO -    Trainable parameters: 2,276,993
