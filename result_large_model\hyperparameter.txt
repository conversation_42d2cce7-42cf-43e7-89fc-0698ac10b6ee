----------start----------
device: 0
save_path: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_large_model
train_files_path: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
val_files_path: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
test_files_path: c:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
input_curves: ['GR', 'AC', 'CNL', 'RLLD']
output_curves: ['DEN']
transform: True
total_seqlen: 720
effect_seqlen: 640
batch_size: 16
learning_rate: 1e-05
epochs: 1000
patience: 150
model_type: large
feature_num: 64
use_pe: True
drop: 0.1
attn_drop: 0.1
position_drop: 0.1
skip_training: False
continue_train: False
checkpoint_path: None
----------end----------
