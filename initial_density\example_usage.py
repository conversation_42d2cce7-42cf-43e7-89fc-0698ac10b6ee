#!/usr/bin/env python3
"""
Example Usage Script for Initial Density Prediction Module

This script demonstrates how to use the initial_density module for:
1. Loading and preprocessing data
2. Training a density prediction model
3. Evaluating model performance
4. Generating comprehensive visualizations

Usage:
    python example_usage.py --data_file path/to/data.hdf5 --mode [train|test|plot]
"""

import os
import sys
import argparse
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np

# Import from the initial_density module
from density_prediction_improved import DensityDataNormalizer, ImprovedDensityDataset, create_density_dataset
from density_prediction_plot_improved import DensityPlotterImproved
from model import MWLT_Small, MWLT_Base, MWLT_Large
from utils import get_device, save_checkpoint, load_checkpoint, EarlyStopping, cal_RMSE, cal_R2


def example_data_loading():
    """
    Example: How to load and preprocess density prediction data
    """
    print("=== Example: Data Loading and Preprocessing ===")
    
    # Create normalizer
    normalizer = DensityDataNormalizer()
    print("Created DensityDataNormalizer")
    print("Normalization parameters:", normalizer.get_normalization_params())
    
    # Example with dummy data file path
    data_file = "../A2.hdf5"  # Adjust path as needed
    
    if os.path.exists(data_file):
        try:
            # Create dataset using convenience function
            dataset = create_density_dataset(
                file_path=data_file,
                input_curves=['GR', 'AC', 'CNL', 'RLLD'],
                output_curves=['DENSITY'],
                sequence_length=640,
                augment=True
            )
            
            print(f"Dataset created successfully with {len(dataset)} samples")
            
            # Get a sample
            if len(dataset) > 0:
                inputs, targets = dataset[0]
                print(f"Sample input shape: {inputs.shape}")
                print(f"Sample target shape: {targets.shape}")
                print(f"Input range: [{inputs.min():.3f}, {inputs.max():.3f}]")
                print(f"Target range: [{targets.min():.3f}, {targets.max():.3f}]")
            
        except Exception as e:
            print(f"Error loading data: {e}")
            print("Using dummy data for demonstration...")
            return create_dummy_dataset()
    else:
        print(f"Data file {data_file} not found. Using dummy data for demonstration...")
        return create_dummy_dataset()
    
    return dataset


def create_dummy_dataset():
    """Create a dummy dataset for demonstration purposes"""
    print("Creating dummy dataset for demonstration...")
    
    # Create synthetic data
    n_samples = 100
    seq_len = 640
    n_input_curves = 4
    n_output_curves = 1
    
    # Generate synthetic well log data
    np.random.seed(42)
    input_data = np.random.randn(n_input_curves, n_samples * seq_len)
    output_data = np.random.randn(n_output_curves, n_samples * seq_len) * 0.1 + 2.5  # Around 2.5 g/cc
    
    class DummyDataset:
        def __init__(self):
            self.input_data = input_data
            self.output_data = output_data
            self.n_samples = n_samples
            self.seq_len = seq_len
            
        def __len__(self):
            return self.n_samples
            
        def __getitem__(self, idx):
            start_idx = idx * self.seq_len
            end_idx = start_idx + self.seq_len
            
            inputs = torch.from_numpy(self.input_data[:, start_idx:end_idx]).float()
            targets = torch.from_numpy(self.output_data[:, start_idx:end_idx]).float()
            
            return inputs, targets
    
    return DummyDataset()


def example_model_training(dataset, model_type="small", epochs=5):
    """
    Example: How to train a density prediction model
    """
    print(f"\n=== Example: Model Training ({model_type.upper()}) ===")
    
    # Setup device
    device = get_device()
    
    # Create model
    if model_type == "small":
        model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640)
    elif model_type == "base":
        model = MWLT_Base(in_channels=4, out_channels=1, seq_len=640)
    elif model_type == "large":
        model = MWLT_Large(in_channels=4, out_channels=1, seq_len=640)
    else:
        raise ValueError(f"Unknown model type: {model_type}")
    
    model = model.to(device)
    print(f"Created {model_type.upper()} model with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # Setup training
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    dataloader = DataLoader(dataset, batch_size=8, shuffle=True)
    
    # Training loop
    model.train()
    for epoch in range(epochs):
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (inputs, targets) in enumerate(dataloader):
            inputs, targets = inputs.to(device), targets.to(device)
            
            optimizer.zero_grad()
            outputs = model(inputs)
            loss = criterion(outputs, targets)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx == 0:  # Print first batch info
                print(f"Epoch {epoch+1}/{epochs}, Batch {batch_idx+1}: Loss = {loss.item():.6f}")
        
        avg_loss = total_loss / num_batches
        print(f"Epoch {epoch+1}/{epochs} completed. Average Loss: {avg_loss:.6f}")
    
    # Save model
    save_path = f"example_model_{model_type}.pth"
    save_checkpoint({
        'model_state_dict': model.state_dict(),
        'epoch': epochs,
        'loss': avg_loss
    }, save_path)
    print(f"Model saved to: {save_path}")
    
    return model, save_path


def example_model_evaluation(model, dataset, model_path=None):
    """
    Example: How to evaluate a trained model
    """
    print("\n=== Example: Model Evaluation ===")
    
    device = get_device()
    
    # Load model if path provided
    if model_path and os.path.exists(model_path):
        model_dict, epoch, loss = load_checkpoint(model_path, device)
        model.load_state_dict(model_dict)
        print(f"Loaded model from {model_path} (epoch {epoch}, loss {loss:.6f})")
    
    model.eval()
    dataloader = DataLoader(dataset, batch_size=16, shuffle=False)
    
    all_predictions = []
    all_targets = []
    
    with torch.no_grad():
        for inputs, targets in dataloader:
            inputs, targets = inputs.to(device), targets.to(device)
            outputs = model(inputs)
            
            all_predictions.extend(outputs.cpu().numpy().flatten())
            all_targets.extend(targets.cpu().numpy().flatten())
    
    all_predictions = np.array(all_predictions)
    all_targets = np.array(all_targets)
    
    # Calculate metrics
    rmse = cal_RMSE(all_predictions, all_targets)
    r2 = cal_R2(all_predictions, all_targets)
    mae = np.mean(np.abs(all_predictions - all_targets))
    
    print(f"Evaluation Results:")
    print(f"  RMSE: {rmse:.6f}")
    print(f"  R²: {r2:.6f}")
    print(f"  MAE: {mae:.6f}")
    print(f"  Prediction range: [{all_predictions.min():.3f}, {all_predictions.max():.3f}]")
    print(f"  Target range: [{all_targets.min():.3f}, {all_targets.max():.3f}]")
    
    return {
        'rmse': rmse,
        'r2': r2,
        'mae': mae,
        'predictions': all_predictions,
        'targets': all_targets
    }


def example_visualization(results_dir="./example_results"):
    """
    Example: How to create comprehensive visualizations
    """
    print(f"\n=== Example: Visualization ===")
    
    # Create results directory
    os.makedirs(results_dir, exist_ok=True)
    
    # Create plotter
    plotter = DensityPlotterImproved(results_dir, save_plots=True)
    print(f"Created plotter with results directory: {results_dir}")
    
    # Note: For full visualization, you would need:
    # 1. Training history CSV file
    # 2. Trained model file
    # 3. Test data file
    
    print("To generate full visualizations, use:")
    print("plotter.plot_training_history()")
    print("plotter.plot_prediction_results(model_path, test_file)")
    print("plotter.plot_error_analysis()")
    print("plotter.plot_input_curves_analysis()")
    
    return plotter


def main():
    """Main function demonstrating the complete workflow"""
    parser = argparse.ArgumentParser(description="Example usage of initial_density module")
    parser.add_argument('--data_file', type=str, default="../A2.hdf5", 
                       help='Path to HDF5 data file')
    parser.add_argument('--mode', type=str, default='all', 
                       choices=['all', 'data', 'train', 'eval', 'plot'],
                       help='Which example to run')
    parser.add_argument('--model_type', type=str, default='small',
                       choices=['small', 'base', 'large'],
                       help='Model architecture to use')
    parser.add_argument('--epochs', type=int, default=5,
                       help='Number of training epochs')
    
    args = parser.parse_args()
    
    print("=== Initial Density Prediction Module - Example Usage ===")
    print(f"Mode: {args.mode}")
    print(f"Data file: {args.data_file}")
    print(f"Model type: {args.model_type}")
    
    # Run examples based on mode
    if args.mode in ['all', 'data']:
        dataset = example_data_loading()
    
    if args.mode in ['all', 'train']:
        if 'dataset' not in locals():
            dataset = example_data_loading()
        model, model_path = example_model_training(dataset, args.model_type, args.epochs)
    
    if args.mode in ['all', 'eval']:
        if 'model' not in locals():
            # Create model for evaluation
            if args.model_type == "small":
                model = MWLT_Small(in_channels=4, out_channels=1, seq_len=640)
            elif args.model_type == "base":
                model = MWLT_Base(in_channels=4, out_channels=1, seq_len=640)
            else:
                model = MWLT_Large(in_channels=4, out_channels=1, seq_len=640)
        if 'dataset' not in locals():
            dataset = example_data_loading()
        
        model_path = f"example_model_{args.model_type}.pth" if 'model_path' not in locals() else model_path
        results = example_model_evaluation(model, dataset, model_path if os.path.exists(model_path) else None)
    
    if args.mode in ['all', 'plot']:
        plotter = example_visualization()
    
    print("\n=== Example completed successfully! ===")
    print("Check the generated files and results directory for outputs.")


if __name__ == "__main__":
    main()
