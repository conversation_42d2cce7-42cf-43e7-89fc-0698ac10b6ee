#!/usr/bin/env python3
"""
Improved Density Prediction Plotting Script

This script provides comprehensive visualization capabilities for the improved density prediction pipeline.
It includes functions for plotting training history, prediction results, error analysis, and model performance.

Usage:
    python density_prediction_plot_improved.py --results_dir ../density_improved_results --test_file ../A2.hdf5
"""

import os
import sys
import argparse
import h5py
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
try:
    plt.style.use('seaborn-v0_8')
except OSError:
    try:
        plt.style.use('seaborn')
    except OSError:
        plt.style.use('default')
        
try:
    sns.set_palette("husl")
except:
    pass

# Set matplotlib backend to non-interactive for better compatibility
plt.ioff()  # Turn off interactive mode

# Import from the improved pipeline
from density_prediction_improved import DensityDataNormalizer, ImprovedDensityDataset
from model import MWLT_Small, MWLT_Base, MWLT_Large
from utils import load_checkpoint, cal_RMSE, cal_R2

import torch


class DensityPlotterImproved:
    """
    Comprehensive plotting class for improved density prediction results
    """
    
    def __init__(self, results_dir: str, save_plots: bool = True):
        self.results_dir = results_dir
        self.save_plots = save_plots
        self.plots_dir = os.path.join(results_dir, "plots")
        
        if save_plots:
            os.makedirs(self.plots_dir, exist_ok=True)
        
        # Set up plotting parameters
        self.figsize_large = (15, 10)
        self.figsize_medium = (12, 8)
        self.figsize_small = (10, 6)
        self.dpi = 300
    
    def plot_training_history(self, history_file: str = None):
        """
        Plot training and validation loss history
        """
        if history_file is None:
            history_file = os.path.join(self.results_dir, "training_history.csv")
        
        if not os.path.exists(history_file):
            print(f"Training history file not found: {history_file}")
            return
        
        # Load training history
        history = pd.read_csv(history_file)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=self.figsize_medium)
        
        # Plot loss curves
        epochs = range(1, len(history) + 1)
        ax1.plot(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
        ax1.plot(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        ax1.set_xlabel('Epoch')
        ax1.set_ylabel('Loss')
        ax1.set_title('Training and Validation Loss')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # Plot loss curves (log scale)
        ax2.semilogy(epochs, history['train_loss'], 'b-', label='Training Loss', linewidth=2)
        ax2.semilogy(epochs, history['val_loss'], 'r-', label='Validation Loss', linewidth=2)
        ax2.set_xlabel('Epoch')
        ax2.set_ylabel('Loss (log scale)')
        ax2.set_title('Training and Validation Loss (Log Scale)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if self.save_plots:
            try:
                plt.savefig(os.path.join(self.plots_dir, "training_history.png"), dpi=self.dpi, bbox_inches='tight')
                print(f"Training history plot saved to: {os.path.join(self.plots_dir, 'training_history.png')}")
            except Exception as e:
                print(f"Warning: Could not save training history plot: {e}")
        
        try:
            plt.show()
        except Exception as e:
            print(f"Warning: Could not display training history plot: {e}")
        
        plt.close(fig)  # Ensure figure is closed to free memory
    
    def plot_prediction_results(self, model_path: str = None, test_file: str = None, 
                              input_curves: List[str] = None, output_curves: List[str] = None,
                              model_type: str = "small", sequence_length: int = 640):
        """
        Plot prediction results vs actual values
        """
        print("Generating prediction results...")
        
        # Set default values if not provided
        if model_path is None:
            model_path = os.path.join(self.results_dir, "best_density_improved_model.pth")
        if test_file is None:
            test_file = "../A2.hdf5"
        if input_curves is None:
            input_curves = ['GR', 'AC', 'CNL', 'RLLD']  # Available curves in A2.hdf5
        if output_curves is None:
            output_curves = ['DENSITY']
        
        # Try to load existing evaluation results first
        eval_file = os.path.join(self.results_dir, 'evaluation_results.npz')
        if os.path.exists(eval_file):
            print("Using existing evaluation results...")
            data = np.load(eval_file)
            all_predictions = data['predictions']
            all_targets = data['targets']
            rmse = float(data['rmse'])
            r2 = float(data['r2'])
            mae = float(data['mae'])
        else:
            try:
                # Setup device and normalizer
                device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
                normalizer = DensityDataNormalizer()
                
                # Create and load model
                if model_type == "small":
                    model = MWLT_Small(in_channels=len(input_curves), out_channels=len(output_curves),
                                     feature_num=64, dim=64, seq_len=sequence_length)
                elif model_type == "base":
                    model = MWLT_Base(in_channels=len(input_curves), out_channels=len(output_curves),
                                    feature_num=64, dim=64, seq_len=sequence_length)
                elif model_type == "large":
                    model = MWLT_Large(in_channels=len(input_curves), out_channels=len(output_curves),
                                     feature_num=128, dim=128, seq_len=sequence_length)
                
                model = model.to(device)
                model_dict, epoch, loss = load_checkpoint(model_path, device)
                model.load_state_dict(model_dict)
                model.eval()
                
                # Load test data
                test_dataset = ImprovedDensityDataset(
                    file_path=test_file,
                    input_curves=input_curves,
                    output_curves=output_curves,
                    normalizer=normalizer,
                    sequence_length=sequence_length,
                    augment=False
                )
                
                # Get predictions
                all_predictions = []
                all_targets = []
                
                with torch.no_grad():
                    for i in range(len(test_dataset)):
                        inputs, targets = test_dataset[i]
                        inputs = inputs.unsqueeze(0).to(device)  # Add batch dimension
                        targets = targets.to(device)
                        
                        # Get normalized predictions
                        predictions_norm = model(inputs)
                        
                        # Denormalize to physical units
                        predictions_phys = normalizer.denormalize_density(predictions_norm.cpu().numpy())
                        targets_phys = normalizer.denormalize_density(targets.cpu().numpy())
                        
                        # Validate physical range
                        predictions_phys = normalizer.validate_density_range(predictions_phys)
                        
                        all_predictions.extend(predictions_phys.flatten())
                        all_targets.extend(targets_phys.flatten())
                
                all_predictions = np.array(all_predictions)
                all_targets = np.array(all_targets)
                
                # Calculate metrics
                rmse = cal_RMSE(all_predictions, all_targets)
                r2 = cal_R2(all_predictions, all_targets)
                mae = np.mean(np.abs(all_predictions - all_targets))
                
                # Save evaluation results for future use
                np.savez(eval_file, predictions=all_predictions, targets=all_targets, 
                        rmse=rmse, r2=r2, mae=mae)
                print(f"Evaluation results saved to: {eval_file}")
                
            except Exception as e:
                print(f"Error during model evaluation: {e}")
                print("Cannot generate prediction plots without evaluation results.")
                return None
        
        # Create comprehensive plots
        fig = plt.figure(figsize=self.figsize_large)
        
        # 1. Scatter plot with 1:1 line
        ax1 = plt.subplot(2, 3, 1)
        plt.scatter(all_targets, all_predictions, alpha=0.6, s=20)
        min_val = min(all_targets.min(), all_predictions.min())
        max_val = max(all_targets.max(), all_predictions.max())
        plt.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=2, label='Perfect Prediction')
        plt.xlabel('Actual Density (g/cc)')
        plt.ylabel('Predicted Density (g/cc)')
        plt.title(f'Predictions vs Actual\nRMSE: {rmse:.4f}, R²: {r2:.4f}')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 2. Residual plot
        ax2 = plt.subplot(2, 3, 2)
        residuals = all_predictions - all_targets
        plt.scatter(all_targets, residuals, alpha=0.6, s=20)
        plt.axhline(y=0, color='r', linestyle='--', linewidth=2)
        plt.xlabel('Actual Density (g/cc)')
        plt.ylabel('Residuals (g/cc)')
        plt.title(f'Residual Plot\nMAE: {mae:.4f}')
        plt.grid(True, alpha=0.3)
        
        # 3. Error histogram
        ax3 = plt.subplot(2, 3, 3)
        plt.hist(residuals, bins=50, alpha=0.7, edgecolor='black')
        plt.axvline(x=0, color='r', linestyle='--', linewidth=2)
        plt.xlabel('Prediction Error (g/cc)')
        plt.ylabel('Frequency')
        plt.title('Error Distribution')
        plt.grid(True, alpha=0.3)
        
        # 4. Time series comparison (first 1000 points)
        ax4 = plt.subplot(2, 3, 4)
        n_points = min(1000, len(all_targets))
        indices = np.arange(n_points)
        plt.plot(indices, all_targets[:n_points], 'b-', label='Actual', linewidth=1.5)
        plt.plot(indices, all_predictions[:n_points], 'r-', label='Predicted', linewidth=1.5, alpha=0.8)
        plt.xlabel('Sample Index')
        plt.ylabel('Density (g/cc)')
        plt.title('Time Series Comparison (First 1000 points)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 5. Density distribution comparison
        ax5 = plt.subplot(2, 3, 5)
        plt.hist(all_targets, bins=30, alpha=0.7, label='Actual', density=True)
        plt.hist(all_predictions, bins=30, alpha=0.7, label='Predicted', density=True)
        plt.xlabel('Density (g/cc)')
        plt.ylabel('Density')
        plt.title('Density Distribution Comparison')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 6. Performance metrics summary
        ax6 = plt.subplot(2, 3, 6)
        ax6.axis('off')
        metrics_text = f"""
        Performance Metrics:
        
        RMSE: {rmse:.4f} g/cc
        R²: {r2:.4f}
        MAE: {mae:.4f} g/cc
        
        Data Statistics:
        Actual Range: [{all_targets.min():.3f}, {all_targets.max():.3f}]
        Predicted Range: [{all_predictions.min():.3f}, {all_predictions.max():.3f}]
        
        Model: {model_type.upper()}
        Samples: {len(all_targets):,}
        """
        ax6.text(0.1, 0.9, metrics_text, transform=ax6.transAxes, fontsize=12,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        if self.save_plots:
            try:
                plt.savefig(os.path.join(self.plots_dir, "prediction_results.png"), dpi=self.dpi, bbox_inches='tight')
                print(f"Prediction results plot saved to: {os.path.join(self.plots_dir, 'prediction_results.png')}")
            except Exception as e:
                print(f"Warning: Could not save prediction results plot: {e}")
        
        try:
            plt.show()
        except Exception as e:
            print(f"Warning: Could not display prediction results plot: {e}")
        
        plt.close(fig)  # Ensure figure is closed to free memory
        
        return {
            'rmse': rmse,
            'r2': r2,
            'mae': mae,
            'predictions': all_predictions,
            'targets': all_targets
        }
    
    def plot_error_analysis(self, predictions: np.ndarray = None, targets: np.ndarray = None):
        """
        Detailed error analysis plots
        """
        # Try to load existing evaluation results if predictions/targets not provided
        if predictions is None or targets is None:
            eval_file = os.path.join(self.results_dir, 'evaluation_results.npz')
            if os.path.exists(eval_file):
                print("Using existing evaluation results for error analysis...")
                try:
                    data = np.load(eval_file)
                    predictions = data['predictions']
                    targets = data['targets']
                except Exception as e:
                    print(f"Error loading evaluation results: {e}")
                    return
            else:
                print("Error: No predictions/targets provided and no evaluation results found.")
                return
        
        residuals = predictions - targets
        abs_errors = np.abs(residuals)
        
        fig, axes = plt.subplots(2, 2, figsize=self.figsize_medium)
        
        # 1. Error vs actual values
        axes[0, 0].scatter(targets, abs_errors, alpha=0.6, s=20)
        axes[0, 0].set_xlabel('Actual Density (g/cc)')
        axes[0, 0].set_ylabel('Absolute Error (g/cc)')
        axes[0, 0].set_title('Absolute Error vs Actual Values')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. Error percentiles
        percentiles = [50, 75, 90, 95, 99]
        error_percentiles = [np.percentile(abs_errors, p) for p in percentiles]
        axes[0, 1].bar(range(len(percentiles)), error_percentiles, 
                      tick_label=[f'{p}th' for p in percentiles])
        axes[0, 1].set_xlabel('Percentile')
        axes[0, 1].set_ylabel('Absolute Error (g/cc)')
        axes[0, 1].set_title('Error Percentiles')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. Q-Q plot for residuals
        try:
            from scipy import stats
            stats.probplot(residuals, dist="norm", plot=axes[1, 0])
            axes[1, 0].set_title('Q-Q Plot of Residuals')
        except ImportError:
            # Fallback if scipy is not available
            axes[1, 0].hist(residuals, bins=30, alpha=0.7, density=True)
            axes[1, 0].set_xlabel('Residuals')
            axes[1, 0].set_ylabel('Density')
            axes[1, 0].set_title('Residuals Distribution (Q-Q plot unavailable)')
        except Exception as e:
            print(f"Warning: Could not create Q-Q plot: {e}")
            axes[1, 0].text(0.5, 0.5, 'Q-Q Plot\nUnavailable', ha='center', va='center', transform=axes[1, 0].transAxes)
            axes[1, 0].set_title('Q-Q Plot of Residuals')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. Cumulative error distribution
        sorted_errors = np.sort(abs_errors)
        cumulative_prob = np.arange(1, len(sorted_errors) + 1) / len(sorted_errors)
        axes[1, 1].plot(sorted_errors, cumulative_prob, linewidth=2)
        axes[1, 1].set_xlabel('Absolute Error (g/cc)')
        axes[1, 1].set_ylabel('Cumulative Probability')
        axes[1, 1].set_title('Cumulative Error Distribution')
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if self.save_plots:
            try:
                plt.savefig(os.path.join(self.plots_dir, "error_analysis.png"), dpi=self.dpi, bbox_inches='tight')
                print(f"Error analysis plot saved to: {os.path.join(self.plots_dir, 'error_analysis.png')}")
            except Exception as e:
                print(f"Warning: Could not save error analysis plot: {e}")
        
        try:
            plt.show()
        except Exception as e:
            print(f"Warning: Could not display error analysis plot: {e}")
        
        plt.close(fig)  # Ensure figure is closed to free memory
    
    def plot_input_curves_analysis(self, test_file: str = None, input_curves: List[str] = None, 
                                 sequence_length: int = 640, max_samples: int = 3):
        """
        Plot input curves analysis
        """
        print("Analyzing input curves...")
        
        # Set default values if not provided
        if test_file is None:
            test_file = "../A2.hdf5"
        if input_curves is None:
            input_curves = ['GR', 'AC', 'CNL', 'RLLD']  # Available curves in A2.hdf5
        
        normalizer = DensityDataNormalizer()
        
        # Load test data
        try:
            test_dataset = ImprovedDensityDataset(
                file_path=test_file,
                input_curves=input_curves,
                output_curves=['DEN'],  # Assuming density output
                normalizer=normalizer,
                sequence_length=sequence_length,
                augment=False
            )
        except Exception as e:
            print(f"Error loading test dataset: {e}")
            return
        
        if len(test_dataset) == 0:
            print(f"No data found in {test_file}. Please check the file path and data.")
            return
            
        n_samples = min(max_samples, len(test_dataset))
        n_curves = len(input_curves)
        
        if n_samples == 0:
            print("No samples available for plotting.")
            return
            
        fig, axes = plt.subplots(n_samples, n_curves, figsize=(4*n_curves, 3*n_samples))
        
        # Handle axes array dimensions properly
        if n_samples == 1 and n_curves == 1:
            axes = np.array([[axes]])
        elif n_samples == 1:
            axes = axes.reshape(1, -1)
        elif n_curves == 1:
            axes = axes.reshape(-1, 1)
        
        for sample_idx in range(n_samples):
            try:
                inputs, targets = test_dataset[sample_idx]
                
                for curve_idx, curve_name in enumerate(input_curves):
                    ax = axes[sample_idx, curve_idx]
                    curve_data = inputs[curve_idx].numpy()
                    
                    ax.plot(curve_data, linewidth=1.5)
                    ax.set_title(f'Sample {sample_idx + 1}: {curve_name}')
                    ax.set_xlabel('Depth Index')
                    ax.set_ylabel('Normalized Value')
                    ax.grid(True, alpha=0.3)
            except Exception as e:
                print(f"Warning: Could not plot sample {sample_idx + 1}: {e}")
                # Fill with placeholder if error occurs
                for curve_idx, curve_name in enumerate(input_curves):
                    ax = axes[sample_idx, curve_idx]
                    ax.text(0.5, 0.5, f'Error loading\nSample {sample_idx + 1}\n{curve_name}', 
                           ha='center', va='center', transform=ax.transAxes)
                    ax.set_title(f'Sample {sample_idx + 1}: {curve_name}')
                    ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        if self.save_plots:
            try:
                plt.savefig(os.path.join(self.plots_dir, "input_curves_analysis.png"), dpi=self.dpi, bbox_inches='tight')
                print(f"Input curves analysis plot saved to: {os.path.join(self.plots_dir, 'input_curves_analysis.png')}")
            except Exception as e:
                print(f"Warning: Could not save input curves analysis plot: {e}")
        
        try:
            plt.show()
        except Exception as e:
            print(f"Warning: Could not display input curves analysis plot: {e}")
        
        plt.close(fig)  # Ensure figure is closed to free memory
    
    def create_comprehensive_report(self, model_path: str, test_file: str,
                                  input_curves: List[str], output_curves: List[str],
                                  model_type: str = "small", sequence_length: int = 640):
        """
        Create a comprehensive visualization report
        """
        print("Creating comprehensive visualization report...")
        
        # 1. Plot training history
        print("1. Plotting training history...")
        self.plot_training_history()
        
        # 2. Plot prediction results
        print("2. Plotting prediction results...")
        results = self.plot_prediction_results(
            model_path, test_file, input_curves, output_curves, model_type, sequence_length
        )
        
        # 3. Plot detailed error analysis
        print("3. Plotting error analysis...")
        self.plot_error_analysis(results['predictions'], results['targets'])
        
        # 4. Plot input curves analysis
        print("4. Plotting input curves analysis...")
        self.plot_input_curves_analysis(test_file, input_curves, sequence_length)
        
        print(f"\nComprehensive report completed!")
        print(f"All plots saved to: {self.plots_dir}")
        
        return results


def parse_arguments():
    """
    Parse command line arguments
    """
    parser = argparse.ArgumentParser(description="Improved Density Prediction Plotting")
    
    # File paths
    parser.add_argument('--results_dir', type=str, default='../density_improved_results',
                       help='Directory containing training results')
    parser.add_argument('--test_file', type=str, default='../A2.hdf5',
                       help='Test data file path')
    parser.add_argument('--model_path', type=str, default=None,
                       help='Path to trained model (auto-detected if not provided)')
    
    # Model configuration
    parser.add_argument('--model_type', type=str, default='small', choices=['small', 'base', 'large'],
                       help='Model architecture type')
    parser.add_argument('--input_curves', nargs='+', default=['GR', 'CNL', 'DEN', 'RLLD'],
                       help='Input curve names')
    parser.add_argument('--output_curves', nargs='+', default=['DEN'],
                       help='Output curve names')
    parser.add_argument('--sequence_length', type=int, default=640,
                       help='Sequence length for model input')
    
    # Plotting options
    parser.add_argument('--save_plots', action='store_true', default=True,
                       help='Save plots to files')
    parser.add_argument('--plot_type', type=str, default='all',
                       choices=['all', 'training', 'prediction', 'error', 'input'],
                       help='Type of plots to generate')
    
    return parser.parse_args()


def main():
    """
    Main function
    """
    args = parse_arguments()
    
    print("=== Improved Density Prediction Plotting ===")
    print(f"Results directory: {args.results_dir}")
    print(f"Test file: {args.test_file}")
    print(f"Model type: {args.model_type}")
    print(f"Plot type: {args.plot_type}")
    
    # Check if results directory exists
    if not os.path.exists(args.results_dir):
        print(f"Error: Results directory not found: {args.results_dir}")
        return
    
    # Auto-detect model path if not provided
    if args.model_path is None:
        model_path = os.path.join(args.results_dir, "best_density_improved_model.pth")
        if not os.path.exists(model_path):
            print(f"Error: Model file not found: {model_path}")
            return
        args.model_path = model_path
    
    # Create plotter
    plotter = DensityPlotterImproved(args.results_dir, args.save_plots)
    
    try:
        if args.plot_type == 'all':
            # Generate all plots
            plotter.plot_training_history()
            result = plotter.plot_prediction_results()
            if result is not None:
                plotter.plot_error_analysis()
            plotter.plot_input_curves_analysis()
            
        elif args.plot_type == 'training':
            plotter.plot_training_history()
            
        elif args.plot_type == 'prediction':
            result = plotter.plot_prediction_results()
            if result is None:
                print("Failed to generate prediction plots.")
                return
            
        elif args.plot_type == 'error':
            plotter.plot_error_analysis()
            
        elif args.plot_type == 'input':
            plotter.plot_input_curves_analysis()
        
        print("\n=== Plotting Completed Successfully! ===")
        
    except Exception as e:
        print(f"Error during plotting: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()