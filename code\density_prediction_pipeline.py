#!/usr/bin/env python3
"""
Integrated Density Prediction Pipeline
Combines training, testing, and visualization into one sequential process

Author: Integrated from train.py, test.py, and plot_density_results.py
Purpose: End-to-end density prediction workflow using MWLT Transformer
"""

import os
import sys
import time
import logging
import argparse
from datetime import datetime
from pathlib import Path

# Core ML imports
import numpy as np
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset
import torch.optim as optim
import pandas as pd
import h5py

# Visualization imports
import matplotlib.pyplot as plt

# Local imports
from model import *
from utils import *


class SingleFileWellDataset(Dataset):
    """Dataset class for loading data from a single HDF5 file"""
    
    def __init__(self, file_path, input_curves, output_curves, transform=False, 
                 total_seqlen=720, effect_seqlen=640, get_file_name=False):
        self.file_path = file_path
        self.input_curves = input_curves
        self.output_curves = output_curves
        self.transform = transform
        self.total_seqlen = total_seqlen
        self.effect_seqlen = effect_seqlen
        self.get_file_name = get_file_name
        
        # Load data from HDF5 file
        self.data = self._load_data()
        
    def _load_data(self):
        """Load data from HDF5 file"""
        data_list = []
        
        with h5py.File(self.file_path, 'r') as f:
            # Load input curves directly from file root
            input_data = []
            for curve in self.input_curves:
                if curve in f:
                    curve_data = f[curve][:]
                    input_data.append(curve_data)
                else:
                    print(f"Warning: Curve {curve} not found in file")
                    return data_list  # Return empty list if any required curve is missing
            
            # Load output curves directly from file root
            output_data = []
            for curve in self.output_curves:
                if curve in f:
                    curve_data = f[curve][:]
                    output_data.append(curve_data)
                else:
                    print(f"Warning: Curve {curve} not found in file")
                    return data_list  # Return empty list if any required curve is missing
            
            # Check if we have all required curves
            if len(input_data) == len(self.input_curves) and len(output_data) == len(self.output_curves):
                # Squeeze and stack the data properly
                # Each curve has shape (1, depth), we need (depth, n_curves)
                input_arrays = [curve.squeeze() for curve in input_data]  # Remove extra dimension
                output_arrays = [curve.squeeze() for curve in output_data]  # Remove extra dimension
                
                input_array = np.column_stack(input_arrays)  # [depth, n_input_curves]
                output_array = np.column_stack(output_arrays)  # [depth, n_output_curves]
                
                # Create sequences
                sequences = self._create_sequences(input_array, output_array, "file_data")
                data_list.extend(sequences)
        
        return data_list
    
    def _create_sequences(self, input_data, output_data, well_name):
        """Create sequences from well data"""
        sequences = []
        data_length = len(input_data)
        
        # Create overlapping sequences
        step_size = self.effect_seqlen // 2  # 50% overlap
        
        for start_idx in range(0, data_length - self.effect_seqlen + 1, step_size):
            end_idx = start_idx + self.effect_seqlen
            
            # Use the same sequence length for both input and output
            input_seq = input_data[start_idx:end_idx]
            output_seq = output_data[start_idx:end_idx]
            
            sequences.append({
                'input': input_seq,
                'output': output_seq,
                'well_name': well_name,
                'start_idx': start_idx
            })
        
        return sequences
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        sample = self.data[idx]
        
        # Convert to tensors with correct shape [sequence_length, features]
        input_seq = torch.FloatTensor(sample['input'])  # [640, 4]
        output_seq = torch.FloatTensor(sample['output'])  # [640, 1]
        
        # Transpose input to match model expectation: [640, 4] -> [4, 640]
        # Model expects [B, C, L] format where C=channels, L=sequence_length
        input_seq = input_seq.T  # [4, 640]
        output_seq = output_seq.T  # [1, 640]
        
        # Apply data augmentation if enabled
        if self.transform:
            # Simple noise augmentation
            noise_factor = 0.01
            input_seq += torch.randn_like(input_seq) * noise_factor
        
        if self.get_file_name:
            file_name = f"{sample['well_name']}_{sample['start_idx']}.hdf5"
            return input_seq, output_seq, file_name
        else:
            return input_seq, output_seq


class DensityPredictionPipeline:
    """
    Integrated pipeline for density prediction using MWLT Transformer
    """
    
    def __init__(self, args):
        self.args = args
        self.device = None
        self.model = None
        self.script_dir = os.path.dirname(os.path.abspath(__file__))
        
        # Setup logging
        self.setup_logging()
        
        # Resolve and validate paths
        self.setup_paths()
        
        # Setup device
        self.setup_device()
        
    def setup_logging(self):
        """Setup logging configuration"""
        log_dir = self.resolve_path(self.args.save_path)
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, f"pipeline_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("Density Prediction Pipeline Started")
        
    def resolve_path(self, path):
        """Resolve relative paths to absolute paths"""
        if os.path.isabs(path):
            return path
        return os.path.normpath(os.path.join(self.script_dir, path))
    
    def setup_paths(self):
        """Setup and validate all required paths"""
        self.logger.info("Setting up and validating paths...")
        
        # Resolve all paths
        self.args.train_file = self.resolve_path(self.args.train_file)
        self.args.val_file = self.resolve_path(self.args.val_file)
        self.args.test_file = self.resolve_path(self.args.test_file)
        self.args.save_path = self.resolve_path(self.args.save_path)
        
        # Also resolve legacy directory paths for backward compatibility
        self.args.train_files_path = self.resolve_path(self.args.train_files_path)
        self.args.val_files_path = self.resolve_path(self.args.val_files_path)
        self.args.test_files_path = self.resolve_path(self.args.test_files_path)
        
        # Create save directory
        os.makedirs(self.args.save_path, exist_ok=True)
        
        # Validate HDF5 files exist
        for file_name, file_path in [
            ("Training file", self.args.train_file),
            ("Validation file", self.args.val_file),
            ("Test file", self.args.test_file)
        ]:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"{file_name} does not exist: {file_path}")
            if not file_path.endswith('.hdf5'):
                raise ValueError(f"{file_name} must be an HDF5 file (.hdf5): {file_path}")
        
        self.logger.info(f"[SUCCESS] Paths validated:")
        self.logger.info(f"   Training: {self.args.train_file}")
        self.logger.info(f"   Validation: {self.args.val_file}")
        self.logger.info(f"   Test: {self.args.test_file}")
        self.logger.info(f"   Save: {self.args.save_path}")
    
    def setup_device(self):
        """Setup computation device"""
        self.device = torch.device(f"cuda:{self.args.device}" if torch.cuda.is_available() else "cpu")
        self.logger.info(f"Using device: {self.device}")
        
        if torch.cuda.is_available():
            self.logger.info(f"   GPU: {torch.cuda.get_device_name(self.device)}")
            self.logger.info(f"   Memory: {torch.cuda.get_device_properties(self.device).total_memory / 1024**3:.1f} GB")
    
    def create_model(self):
        """Create and initialize the model"""
        self.logger.info(f"Creating {self.args.model_type} model...")
        
        if self.args.model_type == "small":
            model = MWLT_Small(
                in_channels=len(self.args.input_curves),
                out_channels=len(self.args.output_curves),
                feature_num=self.args.feature_num,
                use_pe=self.args.use_pe,
                drop=self.args.drop,
                attn_drop=self.args.attn_drop,
                position_drop=self.args.position_drop
            )
        elif self.args.model_type == "base":
            model = MWLT_Base(
                in_channels=len(self.args.input_curves),
                out_channels=len(self.args.output_curves),
                feature_num=self.args.feature_num,
                use_pe=self.args.use_pe,
                drop=self.args.drop,
                attn_drop=self.args.attn_drop,
                position_drop=self.args.position_drop
            )
        elif self.args.model_type == "large":
            model = MWLT_Large(
                in_channels=len(self.args.input_curves),
                out_channels=len(self.args.output_curves),
                feature_num=self.args.feature_num,
                use_pe=self.args.use_pe,
                drop=self.args.drop,
                attn_drop=self.args.attn_drop,
                position_drop=self.args.position_drop
            )
        else:
            raise ValueError(f"Unknown model type: {self.args.model_type}")
        
        self.model = model.to(self.device)
        
        # Count parameters
        total_params = sum(p.numel() for p in self.model.parameters())
        trainable_params = sum(p.numel() for p in self.model.parameters() if p.requires_grad)
        
        self.logger.info(f"   Total parameters: {total_params:,}")
        self.logger.info(f"   Trainable parameters: {trainable_params:,}")
        
        return self.model
    
    def save_hyperparameters(self):
        """Save hyperparameters to file"""
        hyperparams_file = os.path.join(self.args.save_path, "hyperparameter.txt")
        
        with open(hyperparams_file, "w") as f:
            f.write("-" * 10 + "start" + "-" * 10 + "\n")
            for key, value in self.args.__dict__.items():
                f.write(f"{key}: {value}\n")
            f.write("-" * 10 + "end" + "-" * 10 + "\n")
        
        self.logger.info(f"Hyperparameters saved to {hyperparams_file}")
    
    def train_model(self):
        """Training phase"""
        self.logger.info("Starting training phase...")
        
        # Check if model already exists and skip_training is enabled
        model_path = os.path.join(self.args.save_path, "best_model.pth")
        if self.args.skip_training and os.path.exists(model_path):
            self.logger.info(f"Skipping training - model already exists: {model_path}")
            return model_path
        
        # Save hyperparameters
        self.save_hyperparameters()
        
        # Create datasets using specific HDF5 files
        self.logger.info("Loading training and validation datasets...")
        self.logger.info(f"   Training file: {self.args.train_file}")
        self.logger.info(f"   Validation file: {self.args.val_file}")
        
        train_dataset = SingleFileWellDataset(
            file_path=self.args.train_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            transform=self.args.transform,
            total_seqlen=self.args.total_seqlen,
            effect_seqlen=self.args.effect_seqlen
        )
        
        val_dataset = SingleFileWellDataset(
            file_path=self.args.val_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            transform=False,  # No augmentation for validation
            total_seqlen=self.args.total_seqlen,
            effect_seqlen=self.args.effect_seqlen
        )
        
        # Create data loaders
        train_loader = DataLoader(dataset=train_dataset, batch_size=self.args.batch_size, shuffle=True)
        val_loader = DataLoader(dataset=val_dataset, batch_size=1, shuffle=False)
        
        self.logger.info(f"   Training samples: {len(train_dataset)}")
        self.logger.info(f"   Validation samples: {len(val_dataset)}")
        self.logger.info(f"   Batch size: {self.args.batch_size}")
        
        # Create model, optimizer, and loss function
        model = self.create_model()
        optimizer = optim.Adam(model.parameters(), lr=self.args.learning_rate)
        criterion = nn.MSELoss().to(self.device)
        
        # Early stopping
        early_stopping = EarlyStopping(patience=self.args.patience, path=model_path)
        
        # Training loop
        start_epoch = 1
        pd_log = {"train_loss": [], "val_loss": []}
        
        self.logger.info(f"Starting training for {self.args.epochs} epochs...")
        training_start_time = time.time()
        
        for epoch in range(start_epoch, self.args.epochs + 1):
            epoch_start_time = time.time()
            
            # Training
            model.train()
            train_total_loss = 0.0
            
            for step, data in enumerate(train_loader):
                conditions, targets = data
                conditions = conditions.to(self.device)
                targets = targets.to(self.device)
                
                preds = model(conditions)
                loss = criterion(preds, targets)
                
                optimizer.zero_grad()
                loss.backward()
                optimizer.step()
                
                train_total_loss += loss.item()
            
            train_loss_epoch = train_total_loss / len(train_loader)
            
            # Validation
            model.eval()
            val_total_loss = 0.0
            
            with torch.no_grad():
                for step, data in enumerate(val_loader):
                    conditions, targets = data
                    conditions = conditions.to(self.device)
                    targets = targets.to(self.device)
                    
                    preds = model(conditions)
                    loss = criterion(preds, targets)
                    val_total_loss += loss.item()
            
            val_loss_epoch = val_total_loss / len(val_loader)
            
            # Log progress
            pd_log["train_loss"].append(train_loss_epoch)
            pd_log["val_loss"].append(val_loss_epoch)
            
            # Save training state
            state = {
                "model_state_dict": model.state_dict(),
                "loss": val_loss_epoch,
                "epoch": epoch
            }
            
            # Save loss history
            frame_loss = pd.DataFrame(data=pd_log, index=range(start_epoch, epoch + 1))
            frame_loss.to_csv(os.path.join(self.args.save_path, "loss.csv"), index_label="Epoch")
            
            epoch_time = time.time() - epoch_start_time
            self.logger.info(
                f"Epoch [{epoch}/{self.args.epochs}] - "
                f"Train Loss: {train_loss_epoch:.4f}, "
                f"Val Loss: {val_loss_epoch:.4f}, "
                f"Time: {epoch_time:.2f}s"
            )
            
            # Early stopping check
            early_stopping(state, model)
            if early_stopping.early_stop:
                self.logger.info("Early stopping triggered")
                break
        
        training_time = time.time() - training_start_time
        self.logger.info(f"[SUCCESS] Training completed in {training_time:.2f}s")
        self.logger.info(f"Best model saved to: {model_path}")
        
        return model_path
    
    def test_model(self, model_path):
        """Testing phase"""
        self.logger.info("Starting testing phase...")
        
        # Setup test results directory
        test_results_dir = os.path.join(self.args.save_path, "test_results")
        os.makedirs(test_results_dir, exist_ok=True)
        
        # Create test dataset using specific HDF5 file
        self.logger.info("Loading test dataset...")
        self.logger.info(f"   Test file: {self.args.test_file}")
        
        test_dataset = SingleFileWellDataset(
            file_path=self.args.test_file,
            input_curves=self.args.input_curves,
            output_curves=self.args.output_curves,
            transform=False,  # No augmentation for testing
            total_seqlen=self.args.total_seqlen,
            effect_seqlen=self.args.effect_seqlen,
            get_file_name=True
        )
        
        test_loader = DataLoader(dataset=test_dataset, batch_size=1, shuffle=False)
        self.logger.info(f"   Test samples: {len(test_dataset)}")
        
        # Load model
        if not self.model:
            self.create_model()
        
        self.logger.info(f"Loading model from: {model_path}")
        model_dict, epoch, loss = load_checkpoint(model_path, self.device)
        self.model.load_state_dict(model_dict)
        self.model.eval()
        
        self.logger.info(f"   Loaded model from epoch {epoch} with validation loss {loss:.4f}")
        
        # Perform inference
        self.logger.info("Performing inference...")
        count = 0
        total_inference_time = 0
        
        with torch.no_grad():
            for step, data in enumerate(test_loader):
                start_time = time.time()
                
                conditions, targets, file_name = data
                conditions = conditions.to(self.device)
                targets = targets.to(self.device)
                
                # Inference
                inference_start = time.time()
                preds = self.model(conditions)
                inference_time = time.time() - inference_start
                total_inference_time += inference_time
                
                # Convert to numpy
                targets_np = targets.cpu().numpy().squeeze(axis=0)
                preds_np = preds.cpu().numpy().squeeze(axis=0)
                
                # Save results
                result_file = os.path.join(test_results_dir, file_name[0])
                with h5py.File(result_file, "w") as f:
                    f.create_dataset("real", data=targets_np)
                    f.create_dataset("pred", data=preds_np)
                
                count += 1
                step_time = time.time() - start_time
                
                if (step + 1) % 10 == 0 or step == 0:
                    self.logger.info(
                        f"   Processed {step + 1}/{len(test_loader)} files - "
                        f"Inference: {inference_time:.4f}s, Total: {step_time:.4f}s"
                    )
        
        # Performance summary
        avg_inference_time = total_inference_time / count
        self.logger.info(f"[SUCCESS] Testing completed:")
        self.logger.info(f"   Files processed: {count}")
        self.logger.info(f"   Total inference time: {total_inference_time:.4f}s")
        self.logger.info(f"   Average inference time: {avg_inference_time:.4f}s")
        self.logger.info(f"Results saved to: {test_results_dir}")
        
        return test_results_dir
    
    def visualize_results(self, results_dir):
        """Visualization phase"""
        self.logger.info("Starting visualization phase...")
        
        # Setup visualization directory
        viz_dir = os.path.join(self.args.save_path, "visualizations")
        os.makedirs(viz_dir, exist_ok=True)
        
        # Import visualization function
        from plot_density_results import create_density_comparison_plots
        
        try:
            # Create comprehensive plots
            plot_file = create_density_comparison_plots(
                results_dir=results_dir,
                save_dir=viz_dir
            )
            
            if plot_file:
                self.logger.info(f"[SUCCESS] Visualization completed")
                self.logger.info(f"Main plot saved: {plot_file}")
                self.logger.info(f"All visualizations saved to: {viz_dir}")
                return plot_file
            else:
                self.logger.error("[ERROR] Failed to create visualizations")
                return None
                
        except Exception as e:
            self.logger.error(f"[ERROR] Error during visualization: {e}")
            return None
    
    def run_pipeline(self):
        """Run the complete pipeline"""
        pipeline_start_time = time.time()
        
        try:
            self.logger.info("Starting Density Prediction Pipeline")
            self.logger.info(f"   Input curves: {self.args.input_curves}")
            self.logger.info(f"   Output curves: {self.args.output_curves}")
            self.logger.info(f"   Model type: {self.args.model_type}")
            self.logger.info(f"   Training file: {os.path.basename(self.args.train_file)}")
            self.logger.info(f"   Validation file: {os.path.basename(self.args.val_file)}")
            self.logger.info(f"   Test file: {os.path.basename(self.args.test_file)}")
            
            # Phase 1: Training
            model_path = self.train_model()
            
            # Phase 2: Testing
            results_dir = self.test_model(model_path)
            
            # Phase 3: Visualization
            plot_file = self.visualize_results(results_dir)
            
            # Pipeline completion
            pipeline_time = time.time() - pipeline_start_time
            
            self.logger.info("[SUCCESS] Pipeline completed successfully!")
            self.logger.info(f"Total pipeline time: {pipeline_time:.2f}s")
            self.logger.info(f"All outputs saved to: {self.args.save_path}")
            
            return {
                "model_path": model_path,
                "results_dir": results_dir,
                "plot_file": plot_file,
                "pipeline_time": pipeline_time
            }
            
        except Exception as e:
            self.logger.error(f"[ERROR] Pipeline failed: {e}")
            raise


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Integrated Density Prediction Pipeline using MWLT Transformer",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )
    
    # Device and paths
    parser.add_argument("--device", type=str, default="0", help="GPU device number (e.g., 0, 1, 2)")
    parser.add_argument("--save_path", type=str, default="../result_pipeline", help="Directory to save all outputs")
    
    # Specific HDF5 file paths for training and testing
    parser.add_argument("--train_file", type=str, 
                       default="../data_normal/A1.hdf5", 
                       help="HDF5 file for training (e.g., A1.hdf5)")
    parser.add_argument("--val_file", type=str, 
                       default="../data_normal/A1.hdf5", 
                       help="HDF5 file for validation (e.g., A1.hdf5)")
    parser.add_argument("--test_file", type=str, 
                       default="../data_normal/A2.hdf5", 
                       help="HDF5 file for testing (e.g., A2.hdf5)")
    
    # Legacy directory paths (kept for backward compatibility)
    parser.add_argument("--train_files_path", type=str, default="../data_normal/train", help="Training data directory (legacy)")
    parser.add_argument("--val_files_path", type=str, default="../data_normal/val", help="Validation data directory (legacy)")
    parser.add_argument("--test_files_path", type=str, default="../data_normal/val", help="Test data directory (legacy)")
    
    # Data configuration
    parser.add_argument("--input_curves", default=["GR", "AC", "CNL", "RLLD"], type=list, help="Input curve types")
    parser.add_argument("--output_curves", default=["DEN"], type=list, help="Output curve types (density)")
    parser.add_argument("--transform", default=True, type=bool, help="Use data augmentation during training")
    parser.add_argument("--total_seqlen", default=720, type=int, help="Total sequence length")
    parser.add_argument("--effect_seqlen", default=640, type=int, help="Effective sequence length")
    
    # Training configuration
    parser.add_argument("--batch_size", type=int, default=32, help="Training batch size")
    parser.add_argument("--learning_rate", type=float, default=1e-5, help="Learning rate")
    parser.add_argument("--epochs", type=int, default=2000, help="Maximum training epochs")
    parser.add_argument("--patience", type=int, default=150, help="Early stopping patience")
    
    # Model configuration
    parser.add_argument("--model_type", type=str, default="base", choices=["small", "base", "large"], help="Model size")
    parser.add_argument("--feature_num", type=int, default=64, help="Feature dimensions")
    parser.add_argument("--use_pe", type=bool, default=True, help="Use positional embedding")
    parser.add_argument("--drop", type=float, default=0.1, help="Dropout rate")
    parser.add_argument("--attn_drop", type=float, default=0.1, help="Attention dropout rate")
    parser.add_argument("--position_drop", type=float, default=0.1, help="Position embedding dropout rate")
    
    # Pipeline options
    parser.add_argument("--skip_training", action="store_true", help="Skip training if model exists")
    parser.add_argument("--continue_train", type=bool, default=False, help="Continue from checkpoint")
    parser.add_argument("--checkpoint_path", type=str, default=None, help="Path to checkpoint for continuing training")
    
    return parser.parse_args()


def main():
    """Main function"""
    # Parse arguments
    args = parse_arguments()
    
    # Print banner
    print("=" * 80)
    print("DENSITY PREDICTION PIPELINE - MWLT Transformer")
    print("=" * 80)
    print(f"Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Configuration: {args.model_type} model, {args.epochs} max epochs")
    print(f"Data: {args.input_curves} -> {args.output_curves}")
    print(f"Training file: {os.path.basename(args.train_file)}")
    print(f"Validation file: {os.path.basename(args.val_file)}")
    print(f"Test file: {os.path.basename(args.test_file)}")
    print("=" * 80)
    
    try:
        # Create and run pipeline
        pipeline = DensityPredictionPipeline(args)
        results = pipeline.run_pipeline()
        
        # Success summary
        print("\n" + "=" * 80)
        print("[SUCCESS] PIPELINE COMPLETED SUCCESSFULLY!")
        print("=" * 80)
        print(f"Model: {results['model_path']}")
        print(f"Results: {results['results_dir']}")
        print(f"Plots: {results['plot_file']}")
        print(f"Total time: {results['pipeline_time']:.2f}s")
        print("=" * 80)
        
    except Exception as e:
        print(f"\n[ERROR] Pipeline failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()