#!/usr/bin/env python3
"""
Example script to run the Density Prediction Pipeline
Demonstrates different usage scenarios
"""

import os
import sys
import subprocess
from pathlib import Path

def run_pipeline_command(args_list, description):
    """
    Run the pipeline with given arguments
    """
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"{'='*60}")
    
    cmd = ["python", "density_prediction_pipeline.py"] + args_list
    print(f"Command: {' '.join(cmd)}")
    print(f"{'='*60}")
    
    try:
        result = subprocess.run(cmd, check=True, capture_output=False)
        print(f"\n✅ {description} completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"\n❌ {description} failed with exit code {e.returncode}")
        return False
    except Exception as e:
        print(f"\n❌ Error running {description}: {e}")
        return False

def main():
    """
    Main function with different pipeline usage examples
    """
    print("🎯 DENSITY PREDICTION PIPELINE - USAGE EXAMPLES")
    print("=" * 80)
    
    # Change to the code directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    os.chdir(script_dir)
    
    # Example 1: Quick test run (minimal epochs for testing)
    print("\n📋 Available Usage Examples:")
    print("1. Quick test run (1 epoch, small batch)")
    print("2. Standard training run (default settings)")
    print("3. Skip training and use existing model")
    print("4. Large model training")
    print("5. Custom configuration")
    print("6. Show help")
    
    choice = input("\nSelect an option (1-6): ").strip()
    
    if choice == "1":
        # Quick test run
        args = [
            "--epochs", "2",
            "--batch_size", "4",
            "--save_path", "../result_quick_test",
            "--patience", "5"
        ]
        run_pipeline_command(args, "Quick Test Run")
        
    elif choice == "2":
        # Standard training run
        args = [
            "--save_path", "../result_standard",
            "--model_type", "base"
        ]
        run_pipeline_command(args, "Standard Training Run")
        
    elif choice == "3":
        # Skip training
        args = [
            "--skip_training",
            "--save_path", "../result_pipeline",  # Use existing results
        ]
        run_pipeline_command(args, "Skip Training - Use Existing Model")
        
    elif choice == "4":
        # Large model training
        args = [
            "--model_type", "large",
            "--batch_size", "16",  # Smaller batch for large model
            "--save_path", "../result_large_model",
            "--epochs", "1000"
        ]
        run_pipeline_command(args, "Large Model Training")
        
    elif choice == "5":
        # Custom configuration
        print("\n🛠️  Custom Configuration:")
        model_type = input("Model type (small/base/large) [base]: ").strip() or "base"
        epochs = input("Number of epochs [100]: ").strip() or "100"
        batch_size = input("Batch size [32]: ").strip() or "32"
        save_path = input("Save path [../result_custom]: ").strip() or "../result_custom"
        
        args = [
            "--model_type", model_type,
            "--epochs", epochs,
            "--batch_size", batch_size,
            "--save_path", save_path
        ]
        run_pipeline_command(args, "Custom Configuration")
        
    elif choice == "6":
        # Show help
        args = ["--help"]
        run_pipeline_command(args, "Pipeline Help")
        
    else:
        print("❌ Invalid choice. Please select 1-6.")
        return
    
    print("\n" + "=" * 80)
    print("🎉 Pipeline execution completed!")
    print("📁 Check the specified save directory for results.")
    print("📊 Look for:")
    print("   • best_model.pth - Trained model")
    print("   • test_results/ - Prediction results")
    print("   • visualizations/ - Analysis plots")
    print("   • *.log - Execution logs")
    print("=" * 80)

if __name__ == "__main__":
    main()