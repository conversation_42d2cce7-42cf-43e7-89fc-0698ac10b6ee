2025-09-01 13:06:32,706 - INFO - Density Prediction Pipeline Started
2025-09-01 13:06:32,706 - INFO - Setting up and validating paths...
2025-09-01 13:06:32,706 - INFO - [SUCCESS] Paths validated:
2025-09-01 13:06:32,706 - INFO -    Training: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:06:32,706 - INFO -    Validation: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:06:32,706 - INFO -    Test: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:06:32,706 - INFO -    Save: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline
2025-09-01 13:06:32,740 - INFO - Using device: cuda:0
2025-09-01 13:06:32,740 - INFO -    GPU: NVIDIA T550 Laptop GPU
2025-09-01 13:06:32,740 - INFO -    Memory: 4.0 GB
2025-09-01 13:06:32,740 - INFO - Starting Density Prediction Pipeline
2025-09-01 13:06:32,740 - INFO -    Input curves: ['GR', 'AC', 'CNL', 'RLLD']
2025-09-01 13:06:32,740 - INFO -    Output curves: ['DEN']
2025-09-01 13:06:32,745 - INFO -    Model type: base
2025-09-01 13:06:32,745 - INFO -    Training file: A1.hdf5
2025-09-01 13:06:32,745 - INFO -    Validation file: A2.hdf5
2025-09-01 13:06:32,745 - INFO -    Test file: A2.hdf5
2025-09-01 13:06:32,745 - INFO - Starting training phase...
2025-09-01 13:06:32,748 - INFO - Hyperparameters saved to C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline\hyperparameter.txt
2025-09-01 13:06:32,748 - INFO - Loading training and validation datasets...
2025-09-01 13:06:32,748 - INFO -    Training file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:06:32,748 - INFO -    Validation file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:06:32,748 - INFO -    Training samples: 14
2025-09-01 13:06:32,748 - INFO -    Validation samples: 14
2025-09-01 13:06:32,748 - INFO -    Batch size: 32
2025-09-01 13:06:32,748 - INFO - Creating base model...
2025-09-01 13:06:33,057 - INFO -    Total parameters: 926,849
2025-09-01 13:06:33,057 - INFO -    Trainable parameters: 926,849
2025-09-01 13:06:36,022 - INFO - Starting training for 5 epochs...
2025-09-01 13:06:36,022 - ERROR - [ERROR] Pipeline failed: Given groups=1, weight of size [64, 4, 11], expected input[14, 720, 4] to have 4 channels, but got 720 channels instead
