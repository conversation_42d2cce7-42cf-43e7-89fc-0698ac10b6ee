2025-09-01 13:02:27,908 - INFO - Density Prediction Pipeline Started
2025-09-01 13:02:27,908 - INFO - Setting up and validating paths...
2025-09-01 13:02:27,924 - INFO - [SUCCESS] Paths validated:
2025-09-01 13:02:27,924 - INFO -    Training: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:02:27,924 - INFO -    Validation: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:02:27,926 - INFO -    Test: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:02:27,926 - INFO -    Save: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline
2025-09-01 13:02:27,959 - INFO - Using device: cuda:0
2025-09-01 13:02:27,965 - INFO -    GPU: NVIDIA T550 Laptop GPU
2025-09-01 13:02:27,965 - INFO -    Memory: 4.0 GB
2025-09-01 13:02:27,965 - INFO - Starting Density Prediction Pipeline
2025-09-01 13:02:27,965 - INFO -    Input curves: ['GR', 'AC', 'CNL', 'RLLD']
2025-09-01 13:02:27,965 - INFO -    Output curves: ['DEN']
2025-09-01 13:02:27,965 - INFO -    Model type: base
2025-09-01 13:02:27,969 - INFO -    Training file: A1.hdf5
2025-09-01 13:02:27,969 - INFO -    Validation file: A2.hdf5
2025-09-01 13:02:27,969 - INFO -    Test file: A2.hdf5
2025-09-01 13:02:27,983 - INFO - Loading training and validation datasets...
2025-09-01 13:02:27,983 - INFO -    Training file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:02:27,983 - INFO -    Validation file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:02:27,986 - ERROR - [ERROR] Pipeline failed: The truth value of an array with more than one element is ambiguous. Use a.any() or a.all()
