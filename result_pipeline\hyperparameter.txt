----------start----------
device: 0
save_path: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline
train_file: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A1.hdf5
val_file: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A2.hdf5
test_file: c:/Users/<USER>/OneDrive - PT Pertamina (Persero)/13_Python_PKB/4_GIT/MWLT_Orig/main/Transformer/data_normal/A2.hdf5
train_files_path: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\train
val_files_path: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
test_files_path: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\val
input_curves: ['GR', 'AC', 'CNL', 'RLLD']
output_curves: ['DEN']
transform: True
total_seqlen: 720
effect_seqlen: 640
batch_size: 32
learning_rate: 1e-05
epochs: 50
patience: 150
model_type: base
feature_num: 64
use_pe: True
drop: 0.1
attn_drop: 0.1
position_drop: 0.1
skip_training: False
continue_train: False
checkpoint_path: None
----------end----------
