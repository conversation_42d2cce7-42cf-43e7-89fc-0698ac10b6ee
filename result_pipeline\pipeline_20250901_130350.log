2025-09-01 13:03:50,926 - INFO - Density Prediction Pipeline Started
2025-09-01 13:03:50,926 - INFO - Setting up and validating paths...
2025-09-01 13:03:50,926 - INFO - [SUCCESS] Paths validated:
2025-09-01 13:03:50,929 - INFO -    Training: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:03:50,929 - INFO -    Validation: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:03:50,930 - INFO -    Test: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:03:50,930 - INFO -    Save: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline
2025-09-01 13:03:50,965 - INFO - Using device: cuda:0
2025-09-01 13:03:50,968 - INFO -    GPU: NVIDIA T550 Laptop GPU
2025-09-01 13:03:50,968 - INFO -    Memory: 4.0 GB
2025-09-01 13:03:50,968 - INFO - Starting Density Prediction Pipeline
2025-09-01 13:03:50,968 - INFO -    Input curves: ['GR', 'AC', 'CNL', 'RLLD']
2025-09-01 13:03:50,968 - INFO -    Output curves: ['DEN']
2025-09-01 13:03:50,968 - INFO -    Model type: base
2025-09-01 13:03:50,973 - INFO -    Training file: A1.hdf5
2025-09-01 13:03:50,973 - INFO -    Validation file: A2.hdf5
2025-09-01 13:03:50,973 - INFO -    Test file: A2.hdf5
2025-09-01 13:03:50,973 - INFO - Starting training phase...
2025-09-01 13:03:50,973 - INFO - Hyperparameters saved to C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\result_pipeline\hyperparameter.txt
2025-09-01 13:03:50,973 - INFO - Loading training and validation datasets...
2025-09-01 13:03:50,973 - INFO -    Training file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A1.hdf5
2025-09-01 13:03:50,973 - INFO -    Validation file: C:\Users\<USER>\OneDrive - PT Pertamina (Persero)\13_Python_PKB\4_GIT\MWLT_Orig\main\Transformer\data_normal\A2.hdf5
2025-09-01 13:03:50,992 - ERROR - [ERROR] Pipeline failed: num_samples should be a positive integer value, but got num_samples=0
