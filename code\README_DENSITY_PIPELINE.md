# Density Prediction Pipeline

A comprehensive, integrated pipeline that combines training, testing, and visualization for density prediction using Transformer models. This pipeline integrates the functionality of `train.py`, `test.py`, and `plot_density_results.py` into a single, seamless workflow.

## 🎯 Overview

The Density Prediction Pipeline provides an end-to-end solution for:
- **Training** Transformer models (MWLT_Small, MWLT_Base, MWLT_Large) for density prediction
- **Testing** trained models on validation/test datasets
- **Visualizing** results with comprehensive analysis plots and metrics

## 📁 Files

- `density_prediction_pipeline.py` - Main pipeline script
- `run_density_pipeline.py` - Interactive usage examples
- `README_DENSITY_PIPELINE.md` - This documentation

## 🚀 Quick Start

### Basic Usage
```bash
# Run with default settings
python density_prediction_pipeline.py

# Quick test run (minimal epochs)
python density_prediction_pipeline.py --epochs 2 --batch_size 4

# Skip training and use existing model
python density_prediction_pipeline.py --skip_training

# Use interactive examples
python run_density_pipeline.py
```

### View All Options
```bash
python density_prediction_pipeline.py --help
```

## 📋 Command Line Arguments

### Data Configuration
- `--save_path` - Output directory (default: `../result_pipeline`)
- `--train_files_path` - Training data directory (default: `../data_normal/train`)
- `--val_files_path` - Validation data directory (default: `../data_normal/val`)
- `--test_files_path` - Test data directory (default: `../data_normal/val`)
- `--input_curves` - Input curve types (default: `['GR', 'AC', 'CNL', 'RLLD']`)
- `--output_curves` - Output curve types (default: `['DEN']`)

### Model Configuration
- `--model_type` - Model size: `small`, `base`, `large` (default: `base`)
- `--feature_num` - Feature dimensions (default: `64`)
- `--use_pe` - Use positional embedding (default: `True`)
- `--drop` - Dropout rate (default: `0.1`)
- `--attn_drop` - Attention dropout rate (default: `0.1`)
- `--position_drop` - Position embedding dropout rate (default: `0.1`)

### Training Configuration
- `--epochs` - Maximum training epochs (default: `2000`)
- `--batch_size` - Training batch size (default: `32`)
- `--learning_rate` - Learning rate (default: `1e-05`)
- `--patience` - Early stopping patience (default: `150`)
- `--transform` - Use data augmentation (default: `True`)

### Sequence Configuration
- `--total_seqlen` - Total sequence length (default: `720`)
- `--effect_seqlen` - Effective sequence length (default: `640`)

### Pipeline Control
- `--skip_training` - Skip training if model exists (default: `False`)
- `--continue_train` - Continue from checkpoint (default: `False`)
- `--checkpoint_path` - Path to checkpoint for continuing training

## 🔄 Pipeline Workflow

### Phase 1: Setup and Validation
1. **Argument parsing** and validation
2. **Path setup** and directory creation
3. **Device configuration** (CPU/GPU detection)
4. **Logging setup** with timestamped logs

### Phase 2: Training (Optional)
1. **Data loading** with augmentation support
2. **Model creation** (Small/Base/Large variants)
3. **Training loop** with early stopping
4. **Model saving** (best model based on validation loss)
5. **Hyperparameter logging**

### Phase 3: Testing
1. **Model loading** (from training or existing checkpoint)
2. **Test data processing**
3. **Inference** on test dataset
4. **Results saving** in HDF5 format

### Phase 4: Visualization
1. **Results analysis** using integrated plotting functionality
2. **Comprehensive plots** generation:
   - Individual file comparisons
   - Scatter plots (actual vs predicted)
   - Error distributions
   - Performance metrics per file
   - Overall statistics
3. **Report generation** with detailed metrics

## 📊 Output Structure

The pipeline creates a comprehensive output directory:

```
result_pipeline/
├── best_model.pth              # Trained model checkpoint
├── hyperparameter.txt          # Training configuration
├── loss.csv                    # Training/validation loss history
├── pipeline_YYYYMMDD_HHMMSS.log # Execution log
├── test_results/               # Test predictions
│   ├── case_1.hdf5
│   ├── case_2.hdf5
│   └── ...
└── visualizations/             # Analysis plots and reports
    ├── density_prediction_analysis_YYYYMMDD_HHMMSS.png
    └── density_prediction_report_YYYYMMDD_HHMMSS.txt
```

## 🎛️ Usage Examples

### Example 1: Standard Training
```bash
python density_prediction_pipeline.py \
    --model_type base \
    --epochs 1000 \
    --batch_size 32 \
    --save_path ../results_standard
```

### Example 2: Large Model with Custom Settings
```bash
python density_prediction_pipeline.py \
    --model_type large \
    --epochs 2000 \
    --batch_size 16 \
    --learning_rate 5e-06 \
    --patience 200 \
    --save_path ../results_large
```

### Example 3: Quick Testing
```bash
python density_prediction_pipeline.py \
    --epochs 5 \
    --batch_size 8 \
    --patience 3 \
    --save_path ../results_test
```

### Example 4: Skip Training, Use Existing Model
```bash
python density_prediction_pipeline.py \
    --skip_training \
    --save_path ../existing_results
```

## 🔧 Advanced Configuration

### Custom Data Paths
```bash
python density_prediction_pipeline.py \
    --train_files_path /path/to/train \
    --val_files_path /path/to/val \
    --test_files_path /path/to/test \
    --save_path /path/to/results
```

### Custom Curves
```bash
python density_prediction_pipeline.py \
    --input_curves "['GR', 'AC', 'CNL']" \
    --output_curves "['DEN', 'NPHI']"
```

### Continue Training from Checkpoint
```bash
python density_prediction_pipeline.py \
    --continue_train True \
    --checkpoint_path ../previous_results/best_model.pth
```

## 📈 Monitoring and Logging

### Real-time Progress
The pipeline provides comprehensive logging:
- Training progress with loss metrics
- Validation performance
- Early stopping notifications
- Testing progress
- Visualization generation status

### Log Files
- **Console output**: Real-time progress and status
- **Log files**: Detailed execution logs with timestamps
- **CSV files**: Training/validation loss history
- **Text reports**: Comprehensive analysis results

## 🛠️ Troubleshooting

### Common Issues

1. **CUDA Out of Memory**
   ```bash
   # Reduce batch size
   python density_prediction_pipeline.py --batch_size 16
   ```

2. **Data Path Not Found**
   ```bash
   # Check and specify correct paths
   python density_prediction_pipeline.py --train_files_path /correct/path
   ```

3. **Model Loading Error**
   ```bash
   # Ensure model file exists or retrain
   python density_prediction_pipeline.py --skip_training False
   ```

### Performance Tips

1. **GPU Usage**: Ensure CUDA is available for faster training
2. **Batch Size**: Adjust based on available memory
3. **Early Stopping**: Use appropriate patience values
4. **Model Size**: Choose model type based on data complexity

## 🔍 Understanding Results

### Training Metrics
- **Training Loss**: Model performance on training data
- **Validation Loss**: Model performance on validation data
- **Early Stopping**: Prevents overfitting

### Test Results
- **HDF5 Files**: Contain actual and predicted values
- **RMSE**: Root Mean Square Error per file
- **R² Score**: Coefficient of determination
- **MAE**: Mean Absolute Error

### Visualizations
- **Scatter Plots**: Actual vs Predicted correlation
- **Error Distributions**: Understanding prediction errors
- **File Comparisons**: Individual well performance
- **Overall Statistics**: Aggregate performance metrics

## 🤝 Integration with Original Scripts

This pipeline maintains full compatibility with the original scripts:
- **train.py**: All training functionality preserved
- **test.py**: All testing functionality preserved  
- **plot_density_results.py**: All visualization functionality preserved

The pipeline can be used alongside the original scripts or as a complete replacement.

## 📝 Notes

- The pipeline automatically detects GPU availability
- All paths are validated before execution
- Comprehensive error handling prevents crashes
- Results are timestamped for easy tracking
- The pipeline supports both Windows and Unix path formats

## 🎉 Success Indicators

A successful pipeline run will:
1. ✅ Complete training (if not skipped)
2. ✅ Generate test predictions
3. ✅ Create visualization plots
4. ✅ Save all outputs to specified directory
5. ✅ Display completion summary with file paths

---

**Happy Density Prediction! 🎯**